import { describe, it, expect } from 'vitest';
import { detectSale, calculateDiscountPercentage, getSaleDescription } from '../../server/src/utils/sale-detector';
import type { Scraper } from '@shared/schema';

describe('Sale Detector', () => {
  const createMockScraper = (overrides: Partial<Scraper> = {}): Scraper => ({
    id: 'test-id',
    itemName: 'Test Item',
    url: 'https://example.com',
    selector: '.price',
    currentPrice: '100.00',
    lowestPrice: '90.00',
    averagePrice: '120.00',
    status: 'active',
    lastUpdated: new Date(),
    lastError: null,
    createdAt: new Date(),
    isOnSale: false,
    saleStartDate: null,
    priceBeforeSale: null,
    saleThreshold: 0.15,
    ...overrides,
  });

  describe('detectSale', () => {
    it('should detect sale when price drops below threshold', () => {
      const scraper = createMockScraper({
        currentPrice: '100.00', // Current price
        averagePrice: '120.00', // 90-day average
        isOnSale: false, // Not currently on sale
      });

      // 15% below 120 = 102, so 100 should trigger sale
      const result = detectSale('100.00', '120.00', scraper, 0.15);

      expect(result.isOnSale).toBe(true);
      expect(result.saleJustStarted).toBe(true);
      expect(result.saleJustEnded).toBe(false);
      expect(result.priceBeforeSale).toBe('100.00'); // Previous current price
    });

    it('should not detect sale when price is above threshold', () => {
      const scraper = createMockScraper({
        currentPrice: '110.00', // Current price
        averagePrice: '120.00', // 90-day average
        isOnSale: false,
      });

      // 15% below 120 = 102, so 110 should not trigger sale
      const result = detectSale('110.00', '120.00', scraper, 0.15);

      expect(result.isOnSale).toBe(false);
      expect(result.saleJustStarted).toBe(false);
      expect(result.saleJustEnded).toBe(false);
    });

    it('should detect sale ending when price goes above threshold', () => {
      const scraper = createMockScraper({
        currentPrice: '110.00', // Current price (above threshold)
        averagePrice: '120.00', // 90-day average
        isOnSale: true, // Currently on sale
        saleStartDate: new Date('2023-01-01'),
        priceBeforeSale: '125.00',
      });

      const result = detectSale('110.00', '120.00', scraper, 0.15);

      expect(result.isOnSale).toBe(false);
      expect(result.saleJustStarted).toBe(false);
      expect(result.saleJustEnded).toBe(true);
      expect(result.saleStartDate).toBe(null);
      expect(result.priceBeforeSale).toBe(null);
    });

    it('should maintain ongoing sale state', () => {
      const saleStart = new Date('2023-01-01');
      const scraper = createMockScraper({
        currentPrice: '100.00', // Still below threshold
        averagePrice: '120.00',
        isOnSale: true, // Already on sale
        saleStartDate: saleStart,
        priceBeforeSale: '125.00',
      });

      const result = detectSale('100.00', '120.00', scraper, 0.15);

      expect(result.isOnSale).toBe(true);
      expect(result.saleJustStarted).toBe(false);
      expect(result.saleJustEnded).toBe(false);
      expect(result.saleStartDate).toBe(saleStart); // Unchanged
      expect(result.priceBeforeSale).toBe('125.00'); // Unchanged
    });

    it('should handle null prices gracefully', () => {
      const scraper = createMockScraper();

      const result = detectSale(null, '120.00', scraper, 0.15);
      expect(result.isOnSale).toBe(false);

      const result2 = detectSale('100.00', null, scraper, 0.15);
      expect(result2.isOnSale).toBe(false);
    });

    it('should handle invalid prices gracefully', () => {
      const scraper = createMockScraper();

      const result = detectSale('invalid', '120.00', scraper, 0.15);
      expect(result.isOnSale).toBe(false);

      const result2 = detectSale('100.00', 'invalid', scraper, 0.15);
      expect(result2.isOnSale).toBe(false);
    });
  });

  describe('calculateDiscountPercentage', () => {
    it('should calculate discount percentage correctly', () => {
      const discount = calculateDiscountPercentage('100.00', '120.00');
      expect(discount).toBeCloseTo(0.1667, 4); // (120-100)/120 = 16.67%
    });

    it('should return null for invalid prices', () => {
      expect(calculateDiscountPercentage(null, '120.00')).toBe(null);
      expect(calculateDiscountPercentage('100.00', null)).toBe(null);
      expect(calculateDiscountPercentage('invalid', '120.00')).toBe(null);
    });

    it('should handle zero average price', () => {
      expect(calculateDiscountPercentage('100.00', '0')).toBe(null);
    });
  });

  describe('getSaleDescription', () => {
    it('should return "Not on sale" when not on sale', () => {
      const saleResult = {
        isOnSale: false,
        saleStartDate: null,
        priceBeforeSale: null,
        saleJustStarted: false,
        saleJustEnded: false,
        previousSaleState: false,
      };

      const description = getSaleDescription(saleResult, '100.00', '120.00');
      expect(description).toBe('Not on sale');
    });

    it('should return discount percentage when on sale', () => {
      const saleResult = {
        isOnSale: true,
        saleStartDate: new Date(),
        priceBeforeSale: '125.00',
        saleJustStarted: true,
        saleJustEnded: false,
        previousSaleState: false,
      };

      const description = getSaleDescription(saleResult, '100.00', '120.00');
      expect(description).toBe('17% off average price'); // (120-100)/120 = 16.67% rounded to 17%
    });

    it('should handle invalid prices in sale description', () => {
      const saleResult = {
        isOnSale: true,
        saleStartDate: new Date(),
        priceBeforeSale: null,
        saleJustStarted: true,
        saleJustEnded: false,
        previousSaleState: false,
      };

      const description = getSaleDescription(saleResult, null, '120.00');
      expect(description).toBe('On sale');
    });
  });
});
