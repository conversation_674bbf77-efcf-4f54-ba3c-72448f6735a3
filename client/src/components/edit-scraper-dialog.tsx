import { useEffect } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Save, X, Loader2 } from "lucide-react";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { insertScraperSchema } from "@shared/schema";
import type { Scraper, InsertScraper } from "@shared/schema";

interface EditScraperDialogProps {
  scraper: Scraper | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export default function EditScraperDialog({ scraper, open, onOpenChange }: EditScraperDialogProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const form = useForm<InsertScraper>({
    resolver: zodResolver(insertScraperSchema),
    defaultValues: {
      itemName: "",
      url: "",
      selector: "",
      parser: "static",
    },
  });

  const updateMutation = useMutation({
    mutationFn: async (data: InsertScraper) => {
      if (!scraper) throw new Error("No scraper selected");
      const res = await apiRequest("PUT", `/api/scrapers/${scraper.id}`, data);
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/scrapers"] });
      onOpenChange(false);
      toast({
        title: "Scraper updated successfully",
        description: "Your changes have been saved.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to update scraper",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Update form values when scraper changes
  useEffect(() => {
    if (scraper) {
      form.reset({
        itemName: scraper.itemName,
        url: scraper.url,
        selector: scraper.selector ?? "",
        parser: scraper.parser,
      });
    }
  }, [scraper, form]);

  const onSubmit = (data: InsertScraper) => {
    updateMutation.mutate(data);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>
            Edit Scraper
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <div>
            <Label htmlFor="edit-itemName">Item Name</Label>
            <Input
              id="edit-itemName"
              data-testid="input-edit-item-name"
              {...form.register("itemName")}
              className="mt-2"
            />
            {form.formState.errors.itemName && (
              <p className="text-sm text-destructive mt-1">
                {form.formState.errors.itemName.message}
              </p>
            )}
          </div>

          <div>
            <Label htmlFor="edit-url">Target URL</Label>
            <Input
              id="edit-url"
              data-testid="input-edit-url"
              type="url"
              {...form.register("url")}
              className="mt-2"
            />
            {form.formState.errors.url && (
              <p className="text-sm text-destructive mt-1">
                {form.formState.errors.url.message}
              </p>
            )}
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="edit-parser-mode"
              checked={form.watch("parser") === "ai"}
              onCheckedChange={(checked) =>
                form.setValue("parser", checked ? "ai" : "static")
              }
            />
            <Label htmlFor="edit-parser-mode">Use AI Parser</Label>
          </div>

          {form.watch("parser") === "static" && (
            <div>
              <Label htmlFor="edit-selector">Price Selector</Label>
              <Input
                id="edit-selector"
                data-testid="input-edit-selector"
                {...form.register("selector")}
                className="mt-2 font-mono text-sm"
              />
              {form.formState.errors.selector && (
                <p className="text-sm text-destructive mt-1">
                  {form.formState.errors.selector.message}
                </p>
              )}
            </div>
          )}

          <div className="flex gap-3 pt-4">
            <Button
              type="submit"
              data-testid="button-save-changes"
              disabled={updateMutation.isPending}
              className="flex-1"
            >
              {updateMutation.isPending ? (
                <Loader2 className="w-4 h-4 animate-spin mr-2" />
              ) : (
                <Save className="w-4 h-4 mr-2" />
              )}
              {updateMutation.isPending ? "Saving..." : "Save Changes"}
            </Button>

            <Button
              type="button"
              data-testid="button-cancel-edit"
              variant="secondary"
              onClick={() => onOpenChange(false)}
              className="flex-1"
            >
              Cancel
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
