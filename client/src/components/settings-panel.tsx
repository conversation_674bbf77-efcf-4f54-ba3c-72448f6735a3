import { useState, useEffect } from "react";
import { Settings, Save } from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";

interface SettingsConfig {
  saleThreshold: number; // Percentage as decimal (0.15 = 15%)
}

const DEFAULT_SETTINGS: SettingsConfig = {
  saleThreshold: 0.15, // 15% default
};

export function SettingsPanel() {
  const [settings, setSettings] = useState<SettingsConfig>(DEFAULT_SETTINGS);
  const [isExpanded, setIsExpanded] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();

  // Load settings from localStorage on component mount
  useEffect(() => {
    const savedSettings = localStorage.getItem("pricepulse-settings");
    if (savedSettings) {
      try {
        const parsed = JSON.parse(savedSettings);
        setSettings({ ...DEFAULT_SETTINGS, ...parsed });
      } catch (error) {
        console.error("Failed to parse saved settings:", error);
      }
    }
  }, []);

  const handleSaveSettings = async () => {
    setIsSaving(true);
    try {
      // Save to localStorage
      localStorage.setItem("pricepulse-settings", JSON.stringify(settings));
      
      // In a full implementation, you would also send this to the server
      // to update all scrapers' saleThreshold values
      
      toast({
        title: "Settings saved",
        description: "Your sale detection settings have been updated.",
      });
    } catch (error) {
      toast({
        title: "Error saving settings",
        description: "Failed to save your settings. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleThresholdChange = (value: string) => {
    const numValue = parseFloat(value);
    if (!isNaN(numValue) && numValue >= 0 && numValue <= 100) {
      setSettings(prev => ({
        ...prev,
        saleThreshold: numValue / 100, // Convert percentage to decimal
      }));
    }
  };

  return (
    <Card className="shadow-sm">
      <CardHeader 
        className="border-b border-border cursor-pointer"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <CardTitle className="flex items-center justify-between text-lg">
          <div className="flex items-center gap-2">
            <Settings className="text-primary" size={20} />
            Sale Detection Settings
          </div>
          <span className="text-sm font-normal text-muted-foreground">
            {isExpanded ? "Click to collapse" : "Click to expand"}
          </span>
        </CardTitle>
      </CardHeader>

      {isExpanded && (
        <CardContent className="p-6 space-y-6">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="sale-threshold">
                Sale Threshold Percentage
              </Label>
              <div className="flex items-center gap-2">
                <Input
                  id="sale-threshold"
                  type="number"
                  min="0"
                  max="100"
                  step="1"
                  value={Math.round(settings.saleThreshold * 100)}
                  onChange={(e) => handleThresholdChange(e.target.value)}
                  className="w-24"
                />
                <span className="text-sm text-muted-foreground">%</span>
              </div>
              <p className="text-sm text-muted-foreground">
                Items are considered "on sale" when their current price is this percentage 
                below the 90-day average price. Default is 15%.
              </p>
            </div>

            <div className="bg-muted/50 p-4 rounded-lg">
              <h4 className="font-medium mb-2">Example:</h4>
              <p className="text-sm text-muted-foreground">
                If an item's 90-day average is $100 and the threshold is{" "}
                {Math.round(settings.saleThreshold * 100)}%, then prices below{" "}
                ${(100 * (1 - settings.saleThreshold)).toFixed(2)} will be marked as "on sale".
              </p>
            </div>

            <div className="flex items-center gap-2">
              <Button 
                onClick={handleSaveSettings}
                disabled={isSaving}
                size="sm"
              >
                <Save className="w-4 h-4 mr-2" />
                {isSaving ? "Saving..." : "Save Settings"}
              </Button>
              
              <Button 
                variant="outline"
                size="sm"
                onClick={() => setSettings(DEFAULT_SETTINGS)}
              >
                Reset to Default
              </Button>
            </div>
          </div>

          <div className="border-t pt-4">
            <h4 className="font-medium mb-2">How Sale Detection Works:</h4>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• Compares current price to 90-day average price</li>
              <li>• Tracks when sales start and end automatically</li>
              <li>• Shows urgency indicators for recent price changes</li>
              <li>• Displays sale duration for ongoing deals</li>
            </ul>
          </div>
        </CardContent>
      )}
    </Card>
  );
}

/**
 * Hook to get current sale threshold setting
 * @returns Current sale threshold as decimal (e.g., 0.15 for 15%)
 */
export function useSaleThreshold(): number {
  const [threshold, setThreshold] = useState(DEFAULT_SETTINGS.saleThreshold);

  useEffect(() => {
    const savedSettings = localStorage.getItem("pricepulse-settings");
    if (savedSettings) {
      try {
        const parsed = JSON.parse(savedSettings);
        setThreshold(parsed.saleThreshold ?? DEFAULT_SETTINGS.saleThreshold);
      } catch (error) {
        console.error("Failed to parse saved settings:", error);
      }
    }
  }, []);

  return threshold;
}
