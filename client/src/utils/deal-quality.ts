import type { Scraper } from "@shared/schema";

export type DealQuality = 
  | "all-time-low"
  | "excellent-deal" 
  | "good-deal"
  | "fair-price"
  | "high-price"
  | "very-high-price"
  | "unknown";

export interface DealClassification {
  quality: DealQuality;
  label: string;
  description: string;
  color: "gold" | "green" | "blue" | "gray" | "orange" | "red";
  priority: number; // Higher number = higher priority for sorting
}

/**
 * Classifies the deal quality based on current price vs historical data
 * @param scraper Scraper data with price information
 * @returns Deal classification with quality, label, and styling info
 */
export function classifyDeal(scraper: Scraper): DealClassification {
  const currentPrice = scraper.currentPrice;
  const lowestPrice = scraper.lowestPrice;
  const averagePrice = scraper.averagePrice;

  // Default classification when data is insufficient
  const unknownClassification: DealClassification = {
    quality: "unknown",
    label: "Unknown",
    description: "Insufficient price data",
    color: "gray",
    priority: 0,
  };

  if (!currentPrice) {
    return unknownClassification;
  }

  const current = parseFloat(currentPrice);
  if (isNaN(current) || current <= 0) {
    return unknownClassification;
  }

  // Check for all-time low
  if (lowestPrice) {
    const lowest = parseFloat(lowestPrice);
    if (!isNaN(lowest) && Math.abs(current - lowest) < 0.01) {
      return {
        quality: "all-time-low",
        label: "ATL",
        description: "This is the lowest price ever recorded",
        color: "gold",
        priority: 100,
      };
    }
  }

  // If we don't have average price, we can only do basic classification
  if (!averagePrice) {
    if (lowestPrice) {
      const lowest = parseFloat(lowestPrice);
      if (!isNaN(lowest)) {
        const percentAboveLowest = ((current - lowest) / lowest) * 100;
        
        if (percentAboveLowest <= 5) {
          return {
            quality: "excellent-deal",
            label: "Excellent Deal",
            description: "Very close to all-time low",
            color: "green",
            priority: 90,
          };
        } else if (percentAboveLowest <= 15) {
          return {
            quality: "good-deal",
            label: "Good Deal",
            description: "Good price compared to historical low",
            color: "blue",
            priority: 70,
          };
        }
      }
    }
    
    return {
      quality: "fair-price",
      label: "Fair Price",
      description: "Limited price history available",
      color: "gray",
      priority: 50,
    };
  }

  const average = parseFloat(averagePrice);
  if (isNaN(average) || average <= 0) {
    return unknownClassification;
  }

  // Calculate percentage difference from average
  const percentFromAverage = ((current - average) / average) * 100;

  // Classify based on percentage from average
  if (percentFromAverage <= -20) {
    return {
      quality: "excellent-deal",
      label: "Excellent Deal",
      description: `${Math.abs(Math.round(percentFromAverage))}% below average`,
      color: "green",
      priority: 90,
    };
  } else if (percentFromAverage <= -10) {
    return {
      quality: "good-deal",
      label: "Good Deal",
      description: `${Math.abs(Math.round(percentFromAverage))}% below average`,
      color: "blue",
      priority: 70,
    };
  } else if (percentFromAverage <= 10) {
    return {
      quality: "fair-price",
      label: "Fair Price",
      description: "Close to average price",
      color: "gray",
      priority: 50,
    };
  } else if (percentFromAverage <= 25) {
    return {
      quality: "high-price",
      label: "High Price",
      description: `${Math.round(percentFromAverage)}% above average`,
      color: "orange",
      priority: 30,
    };
  } else {
    return {
      quality: "very-high-price",
      label: "Very High Price",
      description: `${Math.round(percentFromAverage)}% above average`,
      color: "red",
      priority: 10,
    };
  }
}

/**
 * Gets CSS classes for deal quality badge styling
 * @param classification Deal classification
 * @returns CSS classes for badge styling
 */
export function getDealQualityBadgeClasses(classification: DealClassification): string {
  const baseClasses = "inline-flex items-center px-3 py-1.5 rounded-lg text-xs font-light text-white shadow-md";

  const colorClasses = {
    gold: "bg-gradient-to-r from-yellow-400 via-yellow-500 to-yellow-600 shadow-yellow-500/25",
    green: "bg-gradient-to-r from-green-500 via-green-600 to-green-700 shadow-green-500/25",
    blue: "bg-gradient-to-r from-blue-500 via-blue-600 to-blue-700 shadow-blue-500/25",
    gray: "bg-gradient-to-r from-gray-500 via-gray-600 to-gray-700 shadow-gray-500/25",
    orange: "bg-gradient-to-r from-orange-500 via-orange-600 to-orange-700 shadow-orange-500/25",
    red: "bg-gradient-to-r from-red-500 via-red-600 to-red-700 shadow-red-500/25",
  };

  return `${baseClasses} ${colorClasses[classification.color]}`;
}

/**
 * Gets text color classes for deal quality
 * @param classification Deal classification
 * @returns CSS classes for text color
 */
export function getDealQualityTextClasses(classification: DealClassification): string {
  const colorClasses = {
    gold: "text-yellow-900 dark:text-yellow-100",
    green: "text-green-900 dark:text-green-100",
    blue: "text-blue-900 dark:text-blue-100",
    gray: "text-gray-900 dark:text-gray-100",
    orange: "text-orange-900 dark:text-orange-100",
    red: "text-red-900 dark:text-red-100",
  };

  return colorClasses[classification.color];
}

/**
 * Sorts scrapers by deal quality priority (best deals first)
 * @param scrapers Array of scrapers with deal classifications
 * @returns Sorted array with best deals first
 */
export function sortByDealQuality<T extends { dealClassification?: DealClassification }>(
  scrapers: T[]
): T[] {
  return [...scrapers].sort((a, b) => {
    const priorityA = a.dealClassification?.priority ?? 0;
    const priorityB = b.dealClassification?.priority ?? 0;
    return priorityB - priorityA; // Higher priority first
  });
}

/**
 * Filters scrapers to show only good deals
 * @param scrapers Array of scrapers with deal classifications
 * @param minQuality Minimum deal quality to include
 * @returns Filtered array with only good deals
 */
export function filterGoodDeals<T extends { dealClassification?: DealClassification }>(
  scrapers: T[],
  minQuality: DealQuality = "good-deal"
): T[] {
  const qualityPriorities: Record<DealQuality, number> = {
    "all-time-low": 100,
    "excellent-deal": 90,
    "good-deal": 70,
    "fair-price": 50,
    "high-price": 30,
    "very-high-price": 10,
    "unknown": 0,
  };

  const minPriority = qualityPriorities[minQuality];
  
  return scrapers.filter(scraper => {
    const priority = scraper.dealClassification?.priority ?? 0;
    return priority >= minPriority;
  });
}
