import { io } from "socket.io-client";

// The URL should match your server's URL.
// Using an environment variable is best practice for production.
const URL =
  process.env.NODE_ENV === "production"
    ? window.location.origin
    : "http://localhost:3000";

export const socket = io(URL, {
  autoConnect: false, // We will connect manually from the component
});

socket.on("connect", () => {
  console.log("Connected to server");
});

socket.on("disconnect", () => {
  console.log("Disconnected from server");
});

export function triggerScraperUpdate(id: string) {
  const apiKey = localStorage.getItem("gemini-api-key");
  socket.emit("scraper:update", id, apiKey);
}

export function triggerAllScrapersUpdate() {
  const apiKey = localStorage.getItem("gemini-api-key");
  socket.emit("scraper:update-all", apiKey);
}
