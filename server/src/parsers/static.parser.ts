import * as cheerio from "cheerio";
import type { <PERSON><PERSON><PERSON>, <PERSON>rse<PERSON><PERSON><PERSON> } from "./types";
import type { <PERSON>rap<PERSON> } from "@shared/schema";

function parsePrice(priceText: string): string | null {
  if (!priceText || typeof priceText !== "string") {
    return null;
  }

  // Clean the input - remove extra whitespace and normalize
  let cleanText = priceText.trim().replace(/\s+/g, " ");

  if (!cleanText) {
    return null;
  }

  // Remove currency symbols and codes to isolate the numeric part
  const numericPart = extractNumericPart(cleanText);

  if (!numericPart) {
    return null;
  }

  // Parse the numeric part based on separator patterns
  const parsedNumber = parseNumericPart(numericPart);

  if (parsedNumber === null || isNaN(parsedNumber) || parsedNumber < 0) {
    return null;
  }

  // Return as string with proper decimal formatting
  return parsedNumber.toString();
}

function extractNumericPart(text: string): string | null {
  // Remove currency symbols from the beginning and end
  let cleaned = text;

  // Remove currency codes (3-letter codes like USD, EUR)
  for (const code of [
    "USD",
    "EUR",
    "GBP",
    "JPY",
    "INR",
    "RUB",
    "KRW",
    "ILS",
    "NGN",
    "CRC",
    "PKR",
    "GHS",
    "UAH",
    "KZT",
    "AZN",
    "GEL",
    "BTC",
  ]) {
    const codeRegex = new RegExp(`\\b${code}\\b`, "gi");
    cleaned = cleaned.replace(codeRegex, "").trim();
  }

  // Remove currency symbols
  for (const symbol of [
    "$",
    "€",
    "£",
    "¥",
    "₹",
    "₽",
    "₩",
    "₪",
    "₦",
    "₡",
    "₨",
    "₵",
    "₴",
    "₸",
    "₼",
    "₾",
    "₿",
  ]) {
    // Escape special regex characters
    const escapedSymbol = symbol.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
    const symbolRegex = new RegExp(escapedSymbol, "g");
    cleaned = cleaned.replace(symbolRegex, "").trim();
  }

  // Extract the numeric part (digits, separators)
  // This regex captures numbers with various separators
  const numericMatch = cleaned.match(/[\d\s,.']+/);

  if (!numericMatch) {
    return null;
  }

  return numericMatch[0].trim();
}

function parseNumericPart(numericText: string): number | null {
  if (!numericText) {
    return null;
  }

  // Handle different separator patterns
  const patterns = [
    // Pattern 1: Comma as decimal separator (European style)
    // Examples: 1.343,88 or 1 343,88 or 1'343,88
    // But NOT 1,343 (which should be thousands separator)
    {
      regex: /^([\d\s.']),((\d{1,2}))$/,
      parse: (match: RegExpMatchArray) => {
        const integerPart = match[1].replace(/[\s.']/g, "");
        const decimalPart = match[2];
        return parseFloat(`${integerPart}.${decimalPart}`);
      },
    },

    // Pattern 2: Dot as decimal separator (US/UK style)
    // Examples: 1,343.88 or 1 343.88 or 1'343.88
    {
      regex: /^([\d\s,']*)\.((\d{1,3}))$/,
      parse: (match: RegExpMatchArray) => {
        const integerPart = match[1].replace(/[\s,']/g, "");
        const decimalPart = match[2];
        return parseFloat(`${integerPart}.${decimalPart}`);
      },
    },

    // Pattern 3: No decimal places, just thousands separators
    // Examples: 1,343 or 1.343 or 1 343 or 1'343
    {
      regex: /^[\d\s,.']+$/,
      parse: (match: RegExpMatchArray) => {
        const fullMatch = match[0];

        // If it contains only digits, it's straightforward
        if (/^\d+$/.test(fullMatch)) {
          return parseFloat(fullMatch);
        }

        // Check if it looks like it has thousands separators
        const parts = fullMatch.split(/[\s,.']/);
        const lastPart = parts[parts.length - 1];

        // If there are exactly 2 parts and the last part has 1-2 digits, it's likely a decimal
        if (parts.length === 2 && lastPart.length <= 2) {
          // Likely decimal separator
          const integerPart = parts[0];
          const decimalPart = lastPart;
          return parseFloat(`${integerPart}.${decimalPart}`);
        } else {
          // Either thousands separators or no decimal places
          // Remove all separators and treat as integer
          const cleaned = fullMatch.replace(/[\s,.']/g, "");
          return parseFloat(cleaned);
        }
      },
    },
  ];

  // Try each pattern
  for (const pattern of patterns) {
    const match = numericText.match(pattern.regex);
    if (match) {
      try {
        const result = pattern.parse(match);
        if (!isNaN(result) && result >= 0) {
          return result;
        }
      } catch (error) {
        // Continue to next pattern
        continue;
      }
    }
  }

  // Fallback: try to parse as a simple number after removing all non-digit characters except the last dot/comma
  const fallbackCleaned = cleanForFallbackParsing(numericText);
  if (fallbackCleaned) {
    const fallbackResult = parseFloat(fallbackCleaned);
    if (!isNaN(fallbackResult) && fallbackResult >= 0) {
      return fallbackResult;
    }
  }

  return null;
}

function cleanForFallbackParsing(text: string): string | null {
  // Find the last occurrence of a potential decimal separator
  const lastDotIndex = text.lastIndexOf(".");
  const lastCommaIndex = text.lastIndexOf(",");

  let decimalSeparatorIndex = -1;

  if (lastDotIndex > lastCommaIndex) {
    decimalSeparatorIndex = lastDotIndex;
  } else if (lastCommaIndex > lastDotIndex) {
    decimalSeparatorIndex = lastCommaIndex;
  }

  if (decimalSeparatorIndex === -1) {
    // No decimal separator, just remove all non-digits
    return text.replace(/\D/g, "");
  }

  // Check if the part after the separator looks like decimals (1-3 digits)
  const afterSeparator = text.substring(decimalSeparatorIndex + 1);
  if (/^\d{1,3}$/.test(afterSeparator)) {
    // Treat as decimal separator
    const beforeSeparator = text
      .substring(0, decimalSeparatorIndex)
      .replace(/\D/g, "");
    return `${beforeSeparator}.${afterSeparator}`;
  } else {
    // Treat as thousands separator, remove all non-digits
    return text.replace(/\D/g, "");
  }
}

export const staticParser: Parser = {
  name: "static",
  async parse(html: string, scraper: Scraper): Promise<ParseResult> {
    if (!scraper.selector) {
      return {
        price: null,
        error: "CSS selector is required for static parser",
      };
    }

    try {
      const $ = cheerio.load(html);
      const priceElement = $(scraper.selector);

      if (priceElement.length === 0) {
        return { price: null, error: "Price element not found" };
      }

      const priceText = priceElement.first().text().trim();
      const price = parsePrice(priceText);

      if (!price) {
        return {
          price: null,
          error: `Could not extract a valid price from text: "${priceText}"`,
        };
      }

      return { price };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";
      return { price: null, error: errorMessage };
    }
  },
};
