import type { <PERSON><PERSON><PERSON>, <PERSON>rseR<PERSON>ult } from "./types";
import type { Scraper } from "@shared/schema";
import {
  GoogleGenerativeAI,
  HarmCategory,
  HarmBlockThreshold,
} from "@google/generative-ai";

const MODEL_NAME = "gemini-2.5-flash-lite";

export const aiParser: Parser = {
  name: "ai",
  async parse(
    html: string,
    scraper: Scraper,
    apiKey?: string
  ): Promise<ParseResult> {
    if (!apiKey) {
      return { price: null, error: "Gemini API key is not provided" };
    }

    try {
      const genAI = new GoogleGenerativeAI(apiKey);
      const model = genAI.getGenerativeModel({ model: MODEL_NAME });

      const prompt = `
        ${html}

        Task: Find the price of the ${
          scraper.itemName
        }. Reply only with the price and nothing else in the format: [whole part].[decimal part]
      `;

      const result = await model.generateContent(prompt);
      const response = result.response;
      const priceText = response.text().trim();

      // Validate that the price is a valid float number
      const price = parseFloat(priceText);
      if (isNaN(price)) {
        return {
          price: null,
          error: `AI returned an invalid price: "${priceText}"`,
        };
      }

      return { price: price.toString() };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";
      return { price: null, error: errorMessage };
    }
  },
};
