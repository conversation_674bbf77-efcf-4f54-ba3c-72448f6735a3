import { gotScraping } from "got-scraping";
import type { <PERSON><PERSON><PERSON><PERSON><PERSON>pt<PERSON>, <PERSON>rap<PERSON><PERSON><PERSON>ult } from "./types";
import type { Scraper } from "@shared/schema";

export const gotScrapingAdapter: ScraperAdapter = {
  name: "gotScraping",
  async scrape(scraper: Scraper): Promise<ScrapeResult> {
    try {
      // Use got-scraping with minimal configuration to leverage its anti-detection features
      const response = await gotScraping.get(scraper.url, {
        timeout: { request: 15000 }, // Only increase timeout, let got-scraping handle the rest
      });

      const html = response.body;

      return { html };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";
      console.log(`[GOT-SCRAPING] Failed for ${scraper.url}: ${errorMessage}`);
      return { html: null, error: errorMessage };
    }
  },
};
