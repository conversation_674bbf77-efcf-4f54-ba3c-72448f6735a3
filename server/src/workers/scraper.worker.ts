import { parentPort, workerData } from "worker_threads";
import { getAdapter } from "../adapters";
import { getParser } from "../parsers";
import type { ScrapeResult } from "../adapters/types";
import type { Scraper } from "@shared/schema";

interface WorkerJobData {
  scraper: Scraper;
  primaryAdapter: string;
  fallbackAdapter: string;
  apiKey?: string;
}

async function run() {
  if (!parentPort)
    throw new Error("This script must be run as a worker thread.");

  const { scraper, primaryAdapter, fallbackAdapter, apiKey }: WorkerJobData =
    workerData;

  console.log(`[WORKER] Job ${scraper.id}: Starting scrape for ${scraper.url}`);

  try {
    // Step 1: Try primary adapter (got-scraping)
    console.log(
      `[WORKER] Job ${scraper.id}: Step 1 - Trying primary adapter (${primaryAdapter})`
    );
    const primaryAdapterInstance = getAdapter(primaryAdapter);

    if (!primaryAdapterInstance) {
      throw new Error(`Primary adapter "${primaryAdapter}" not found.`);
    }

    let result: ScrapeResult = await primaryAdapterInstance.scrape(scraper);

    // Step 2: If primary fails, try fallback adapter (playwright)
    if (!result.html) {
      console.log(
        `[WORKER] Job ${
          scraper.id
        }: Step 2 - Primary adapter failed (${
          result.error || "no html found"
        }). Trying fallback adapter (${fallbackAdapter})`
      );

      const fallbackAdapterInstance = getAdapter(fallbackAdapter);
      if (!fallbackAdapterInstance) {
        throw new Error(`Fallback adapter "${fallbackAdapter}" not found.`);
      }

      result = await fallbackAdapterInstance.scrape(scraper);
    }

    if (!result.html) {
      console.log(
        `[WORKER] Job ${scraper.id}: Step 3 - Both adapters failed. Final error: ${
          result.error || "no html found"
        }`
      );
      parentPort.postMessage({
        price: null,
        error: `Both ${primaryAdapter} and ${fallbackAdapter} failed to extract html. Last error: ${
          result.error || "no html found"
        }`,
      });
      return;
    }

    const parser = getParser(scraper.parser);
    const parsedResult = await parser.parse(result.html, scraper, apiKey);

    if (!parsedResult.price) {
      console.log(
        `[WORKER] Job ${scraper.id}: Step 3 - Parsing failed. Final error: ${
          parsedResult.error || "no price found"
        }`
      );
      parentPort.postMessage({
        price: null,
        error: `Parsing failed. Last error: ${
          parsedResult.error || "no price found"
        }`,
      });
      return;
    }

    console.log(
      `[WORKER] Job ${scraper.id}: Completed successfully, sending result to main thread`
    );
    // Send result back to main thread
    parentPort.postMessage({ price: parsedResult.price });
  } catch (error) {
    console.error(`Job ${scraper.id}: Worker execution failed:`, error);

    const result: ScrapeResult = {
      html: null,
      error:
        error instanceof Error ? error.message : "Worker execution failed",
    };

    parentPort.postMessage(result);
  }
}

run();
