import type { PriceHistory } from "@shared/schema";

/**
 * Calculate the average price from the last 90 days of price history
 * @param priceHistory Array of price history entries for a scraper
 * @param scraperId Optional scraper ID for logging purposes
 * @param enableLogging Whether to enable logging (default: false)
 * @returns Average price as string or null if no valid prices found
 */
export function calculate90DayAverage(priceHistory: PriceHistory[], scraperId?: string, enableLogging: boolean = false): string | null {
  if (!priceHistory || priceHistory.length === 0) {
    return null;
  }

  // Calculate the date 90 days ago
  const ninetyDaysAgo = new Date();
  ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);

  // Filter price history to only include entries from the last 90 days
  // and only include entries with valid prices
  const validRecentHistory = priceHistory.filter(entry => {
    if (!entry || !entry.timestamp || !entry.price) {
      return false;
    }

    const entryDate = new Date(entry.timestamp);
    if (isNaN(entryDate.getTime())) {
      return false;
    }

    const price = parseFloat(entry.price);
    if (isNaN(price) || price <= 0) {
      return false;
    }

    return entryDate >= ninetyDaysAgo;
  });

  if (validRecentHistory.length === 0) {
    return null;
  }

  // Calculate the average price
  let totalPrice = 0;

  for (const entry of validRecentHistory) {
    totalPrice += parseFloat(entry.price);
  }

  const averagePrice = totalPrice / validRecentHistory.length;
  const result = averagePrice.toFixed(2);

  // Only log if explicitly enabled and scraper ID is provided
  if (enableLogging && scraperId) {
    console.log(`[AVERAGE-CALC] Scraper ${scraperId}: Calculated 90-day average ${result} from ${validRecentHistory.length} price entries`);
  }

  // Return the average price rounded to 2 decimal places
  return result;
}

/**
 * Calculate various price statistics for a scraper
 * @param priceHistory Array of price history entries for a scraper
 * @param scraperId Optional scraper ID for logging purposes
 * @param enableLogging Whether to enable logging (default: false)
 * @returns Object containing current, lowest, and average prices
 */
export function calculatePriceStatistics(priceHistory: PriceHistory[], scraperId?: string, enableLogging: boolean = false) {
  if (!priceHistory || priceHistory.length === 0) {
    return {
      currentPrice: null,
      lowestPrice: null,
      averagePrice: null,
    };
  }

  // Sort by timestamp descending (newest first)
  const sortedHistory = [...priceHistory].sort((a, b) => 
    new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
  );

  // Get current price (most recent entry)
  const currentPrice = sortedHistory[0]?.price || null;

  // Calculate lowest price from all history
  let lowestPrice: string | null = null;
  for (const entry of priceHistory) {
    const price = parseFloat(entry.price);
    if (!isNaN(price) && price > 0) {
      if (lowestPrice === null || price < parseFloat(lowestPrice)) {
        lowestPrice = entry.price;
      }
    }
  }



  // Calculate 90-day average
  const averagePrice = calculate90DayAverage(priceHistory, scraperId, enableLogging);

  return {
    currentPrice,
    lowestPrice,
    averagePrice,
  };
}
