import type { PriceHistory } from "@shared/schema";

export interface PriceChangeInfo {
  hasRecentChange: boolean;
  changeAmount: number; // Positive for increase, negative for decrease
  changePercentage: number;
  changeDirection: "increase" | "decrease" | "stable";
  previousPrice: string | null;
  hoursAgo: number;
}

export interface SaleDurationInfo {
  isOnSale: boolean;
  saleDurationDays: number;
  saleDurationHours: number;
  saleStartDate: Date | null;
}

/**
 * Analyzes recent price changes within the last 24 hours
 * @param priceHistory Array of price history entries
 * @param currentPrice Current price
 * @returns Information about recent price changes
 */
export function analyzeRecentPriceChange(
  priceHistory: PriceHistory[],
  currentPrice: string | null
): PriceChangeInfo {
  const defaultResult: PriceChangeInfo = {
    hasRecentChange: false,
    changeAmount: 0,
    changePercentage: 0,
    changeDirection: "stable",
    previousPrice: null,
    hoursAgo: 0,
  };

  if (!currentPrice || !priceHistory || priceHistory.length === 0) {
    return defaultResult;
  }

  const current = parseFloat(currentPrice);
  if (isNaN(current)) {
    return defaultResult;
  }

  // Sort price history by timestamp descending (newest first)
  const sortedHistory = [...priceHistory].sort((a, b) => 
    new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
  );

  // Find the most recent price entry that's different from current price
  // and within the last 24 hours
  const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
  
  let previousPrice: string | null = null;
  let previousTimestamp: Date | null = null;

  for (const entry of sortedHistory) {
    const entryDate = new Date(entry.timestamp);
    const entryPrice = parseFloat(entry.price);
    
    // Skip invalid entries
    if (isNaN(entryDate.getTime()) || isNaN(entryPrice)) {
      continue;
    }

    // Skip entries older than 24 hours
    if (entryDate < twentyFourHoursAgo) {
      break;
    }

    // Skip if price is the same as current (no change)
    if (Math.abs(entryPrice - current) < 0.01) {
      continue;
    }

    // Found a different price within 24 hours
    previousPrice = entry.price;
    previousTimestamp = entryDate;
    break;
  }

  // No recent price change found
  if (!previousPrice || !previousTimestamp) {
    return defaultResult;
  }

  const previous = parseFloat(previousPrice);
  const changeAmount = current - previous;
  const changePercentage = previous > 0 ? (changeAmount / previous) * 100 : 0;
  const changeDirection = changeAmount > 0 ? "increase" : changeAmount < 0 ? "decrease" : "stable";
  const hoursAgo = (Date.now() - previousTimestamp.getTime()) / (1000 * 60 * 60);

  return {
    hasRecentChange: true,
    changeAmount,
    changePercentage,
    changeDirection,
    previousPrice,
    hoursAgo,
  };
}

/**
 * Calculates sale duration information
 * @param saleStartDate When the sale started
 * @param isOnSale Whether the item is currently on sale
 * @returns Sale duration information
 */
export function calculateSaleDuration(
  saleStartDate: Date | null,
  isOnSale: boolean
): SaleDurationInfo {
  const defaultResult: SaleDurationInfo = {
    isOnSale,
    saleDurationDays: 0,
    saleDurationHours: 0,
    saleStartDate,
  };

  if (!isOnSale || !saleStartDate) {
    return defaultResult;
  }

  const now = new Date();
  const saleStart = new Date(saleStartDate);
  
  if (isNaN(saleStart.getTime())) {
    return defaultResult;
  }

  const durationMs = now.getTime() - saleStart.getTime();
  const durationHours = durationMs / (1000 * 60 * 60);
  const durationDays = Math.floor(durationHours / 24);

  return {
    isOnSale,
    saleDurationDays: durationDays,
    saleDurationHours: Math.floor(durationHours),
    saleStartDate,
  };
}

/**
 * Determines if a price change is significant enough to highlight
 * @param changeAmount Absolute change amount
 * @param changePercentage Percentage change
 * @param minimumAmount Minimum dollar amount to consider significant (default: $10)
 * @param minimumPercentage Minimum percentage to consider significant (default: 2%)
 * @returns Whether the change is significant
 */
export function isSignificantPriceChange(
  changeAmount: number,
  changePercentage: number,
  minimumAmount: number = 10,
  minimumPercentage: number = 2
): boolean {
  const absoluteAmount = Math.abs(changeAmount);
  const absolutePercentage = Math.abs(changePercentage);
  
  return absoluteAmount >= minimumAmount || absolutePercentage >= minimumPercentage;
}

/**
 * Formats a price change for display
 * @param changeAmount Change amount (positive for increase, negative for decrease)
 * @param showSign Whether to show + sign for positive changes
 * @returns Formatted price change string
 */
export function formatPriceChange(changeAmount: number, showSign: boolean = true): string {
  const sign = changeAmount > 0 ? (showSign ? "+" : "") : "";
  return `${sign}$${Math.abs(changeAmount).toFixed(2)}`;
}

/**
 * Formats sale duration for display
 * @param saleDuration Sale duration information
 * @returns Human-readable sale duration string
 */
export function formatSaleDuration(saleDuration: SaleDurationInfo): string {
  if (!saleDuration.isOnSale) {
    return "";
  }

  if (saleDuration.saleDurationDays >= 1) {
    return `${saleDuration.saleDurationDays} day${saleDuration.saleDurationDays === 1 ? "" : "s"}`;
  } else if (saleDuration.saleDurationHours >= 1) {
    return `${saleDuration.saleDurationHours} hour${saleDuration.saleDurationHours === 1 ? "" : "s"}`;
  } else {
    return "Less than 1 hour";
  }
}
