export type DealQuality = 
  | "all-time-low"
  | "excellent-deal" 
  | "good-deal"
  | "fair-price"
  | "high-price"
  | "very-high-price"
  | "unknown";

export interface DealClassification {
  quality: DealQuality;
  label: string;
  description: string;
  color: "gold" | "green" | "blue" | "gray" | "orange" | "red";
  priority: number; // Higher number = higher priority for sorting
}

/**
 * Classifies the deal quality based on current price vs historical data
 * @param currentPrice Current price of the item
 * @param lowestPrice All-time lowest price
 * @param averagePrice 90-day average price
 * @returns Deal classification with quality, label, and styling info
 */
export function classifyDeal(
  currentPrice: string | null,
  lowestPrice: string | null,
  averagePrice: string | null
): DealClassification {
  // Default classification when data is insufficient
  const unknownClassification: DealClassification = {
    quality: "unknown",
    label: "Unknown",
    description: "Insufficient price data",
    color: "gray",
    priority: 0,
  };

  if (!currentPrice) {
    return unknownClassification;
  }

  const current = parseFloat(currentPrice);
  if (isNaN(current) || current <= 0) {
    return unknownClassification;
  }

  // Check for all-time low
  if (lowestPrice) {
    const lowest = parseFloat(lowestPrice);
    if (!isNaN(lowest) && Math.abs(current - lowest) < 0.01) {
      return {
        quality: "all-time-low",
        label: "ATL",
        description: "This is the lowest price ever recorded",
        color: "gold",
        priority: 100,
      };
    }
  }

  // If we don't have average price, we can only do basic classification
  if (!averagePrice) {
    if (lowestPrice) {
      const lowest = parseFloat(lowestPrice);
      if (!isNaN(lowest)) {
        const percentAboveLowest = ((current - lowest) / lowest) * 100;
        
        if (percentAboveLowest <= 5) {
          return {
            quality: "excellent-deal",
            label: "Excellent Deal",
            description: "Very close to all-time low",
            color: "green",
            priority: 90,
          };
        } else if (percentAboveLowest <= 15) {
          return {
            quality: "good-deal",
            label: "Good Deal",
            description: "Good price compared to historical low",
            color: "blue",
            priority: 70,
          };
        }
      }
    }
    
    return {
      quality: "fair-price",
      label: "Fair Price",
      description: "Limited price history available",
      color: "gray",
      priority: 50,
    };
  }

  const average = parseFloat(averagePrice);
  if (isNaN(average) || average <= 0) {
    return unknownClassification;
  }

  // Calculate percentage difference from average
  const percentFromAverage = ((current - average) / average) * 100;

  // Classify based on percentage from average
  if (percentFromAverage <= -20) {
    return {
      quality: "excellent-deal",
      label: "Excellent Deal",
      description: `${Math.abs(Math.round(percentFromAverage))}% below average`,
      color: "green",
      priority: 90,
    };
  } else if (percentFromAverage <= -10) {
    return {
      quality: "good-deal",
      label: "Good Deal",
      description: `${Math.abs(Math.round(percentFromAverage))}% below average`,
      color: "blue",
      priority: 70,
    };
  } else if (percentFromAverage <= 10) {
    return {
      quality: "fair-price",
      label: "Fair Price",
      description: "Close to average price",
      color: "gray",
      priority: 50,
    };
  } else if (percentFromAverage <= 25) {
    return {
      quality: "high-price",
      label: "High Price",
      description: `${Math.round(percentFromAverage)}% above average`,
      color: "orange",
      priority: 30,
    };
  } else {
    return {
      quality: "very-high-price",
      label: "Very High Price",
      description: `${Math.round(percentFromAverage)}% above average`,
      color: "red",
      priority: 10,
    };
  }
}

/**
 * Gets CSS classes for deal quality badge styling
 * @param classification Deal classification
 * @returns Object with CSS classes for different styling needs
 */
export function getDealQualityStyles(classification: DealClassification) {
  const baseClasses = "inline-flex items-center px-2 py-1 rounded-md text-xs font-medium";
  
  const colorClasses = {
    gold: "bg-yellow-100 text-yellow-800 border border-yellow-200",
    green: "bg-green-100 text-green-800 border border-green-200", 
    blue: "bg-blue-100 text-blue-800 border border-blue-200",
    gray: "bg-gray-100 text-gray-800 border border-gray-200",
    orange: "bg-orange-100 text-orange-800 border border-orange-200",
    red: "bg-red-100 text-red-800 border border-red-200",
  };

  const darkColorClasses = {
    gold: "dark:bg-yellow-900 dark:text-yellow-200 dark:border-yellow-700",
    green: "dark:bg-green-900 dark:text-green-200 dark:border-green-700",
    blue: "dark:bg-blue-900 dark:text-blue-200 dark:border-blue-700", 
    gray: "dark:bg-gray-800 dark:text-gray-200 dark:border-gray-600",
    orange: "dark:bg-orange-900 dark:text-orange-200 dark:border-orange-700",
    red: "dark:bg-red-900 dark:text-red-200 dark:border-red-700",
  };

  return {
    badge: `${baseClasses} ${colorClasses[classification.color]} ${darkColorClasses[classification.color]}`,
    text: classification.color === "gold" ? "text-yellow-800 dark:text-yellow-200" : 
          classification.color === "green" ? "text-green-800 dark:text-green-200" :
          classification.color === "blue" ? "text-blue-800 dark:text-blue-200" :
          classification.color === "orange" ? "text-orange-800 dark:text-orange-200" :
          classification.color === "red" ? "text-red-800 dark:text-red-200" :
          "text-gray-800 dark:text-gray-200",
  };
}

/**
 * Sorts scrapers by deal quality priority (best deals first)
 * @param scrapers Array of scrapers with deal classifications
 * @returns Sorted array with best deals first
 */
export function sortByDealQuality<T extends { dealClassification?: DealClassification }>(
  scrapers: T[]
): T[] {
  return [...scrapers].sort((a, b) => {
    const priorityA = a.dealClassification?.priority ?? 0;
    const priorityB = b.dealClassification?.priority ?? 0;
    return priorityB - priorityA; // Higher priority first
  });
}
