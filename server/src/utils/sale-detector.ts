import type { Scraper, PriceHistory } from "@shared/schema";

export interface SaleDetectionResult {
  isOnSale: boolean;
  saleStartDate: Date | null;
  priceBeforeSale: string | null;
  saleJustStarted: boolean;
  saleJustEnded: boolean;
  previousSaleState: boolean;
}

/**
 * Detects if an item is on sale based on current price vs 90-day average
 * @param currentPrice Current price of the item
 * @param averagePrice 90-day average price
 * @param previousScraper Previous scraper state (for tracking sale transitions)
 * @param saleThreshold Sale threshold (e.g., 0.15 for 15% below average)
 * @returns Sale detection result with state changes
 */
export function detectSale(
  currentPrice: string | null,
  averagePrice: string | null,
  previousScraper: Scraper,
  saleThreshold: number = 0.15
): SaleDetectionResult {
  // Default result when no sale can be detected
  const defaultResult: SaleDetectionResult = {
    isOnSale: previousScraper.isOnSale || false,
    saleStartDate: previousScraper.saleStartDate || null,
    priceBeforeSale: previousScraper.priceBeforeSale || null,
    saleJustStarted: false,
    saleJustEnded: false,
    previousSaleState: previousScraper.isOnSale || false,
  };

  // Can't detect sale without valid prices
  if (!currentPrice || !averagePrice) {
    return defaultResult;
  }

  const current = parseFloat(currentPrice);
  const average = parseFloat(averagePrice);

  // Invalid prices
  if (isNaN(current) || isNaN(average) || current <= 0 || average <= 0) {
    return defaultResult;
  }

  // Calculate the sale threshold price
  const saleThresholdPrice = average * (1 - saleThreshold);
  const isCurrentlyOnSale = current < saleThresholdPrice;
  const wasOnSale = previousScraper.isOnSale || false;

  // Determine state transitions
  const saleJustStarted = isCurrentlyOnSale && !wasOnSale;
  const saleJustEnded = !isCurrentlyOnSale && wasOnSale;

  let saleStartDate = previousScraper.saleStartDate;
  let priceBeforeSale = previousScraper.priceBeforeSale;

  // Sale just started
  if (saleJustStarted) {
    saleStartDate = new Date();
    // Use the previous current price as the price before sale, or the average if not available
    priceBeforeSale = previousScraper.currentPrice || averagePrice;
  }

  // Sale just ended - clear sale data
  if (saleJustEnded) {
    saleStartDate = null;
    priceBeforeSale = null;
  }

  return {
    isOnSale: isCurrentlyOnSale,
    saleStartDate,
    priceBeforeSale,
    saleJustStarted,
    saleJustEnded,
    previousSaleState: wasOnSale,
  };
}

/**
 * Calculate the percentage discount from the average price
 * @param currentPrice Current price
 * @param averagePrice Average price
 * @returns Discount percentage (e.g., 0.15 for 15% off)
 */
export function calculateDiscountPercentage(
  currentPrice: string | null,
  averagePrice: string | null
): number | null {
  if (!currentPrice || !averagePrice) return null;

  const current = parseFloat(currentPrice);
  const average = parseFloat(averagePrice);

  if (isNaN(current) || isNaN(average) || average <= 0) return null;

  return (average - current) / average;
}

/**
 * Get a human-readable description of the sale status
 * @param saleResult Sale detection result
 * @param currentPrice Current price
 * @param averagePrice Average price
 * @returns Human-readable sale description
 */
export function getSaleDescription(
  saleResult: SaleDetectionResult,
  currentPrice: string | null,
  averagePrice: string | null
): string {
  if (!saleResult.isOnSale) {
    return "Not on sale";
  }

  const discount = calculateDiscountPercentage(currentPrice, averagePrice);
  if (discount === null) {
    return "On sale";
  }

  const discountPercent = Math.round(discount * 100);
  return `${discountPercent}% off average price`;
}
